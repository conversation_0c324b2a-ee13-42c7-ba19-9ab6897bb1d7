package main

import (
	"bytes"
	"context"
	"fmt"
	"log"
	"math"
	"net/http"
	_ "net/http/pprof"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/panjf2000/gnet/v2"
)

const (
	// Connection timeout duration (30 seconds)
	connectionTimeout = 30 * time.Second
	// Cleanup interval for checking timeouts (every 5 seconds)
	cleanupInterval = 5 * time.Second
	// UI update interval (every 1 second)
	uiUpdateInterval = 1 * time.Second

	// Performance optimization constants
	maxHeaderSize  = 8192  // Maximum HTTP header size to process
	maxRequestSize = 16384 // Maximum request size to prevent DoS

	// Connection map sharding constants
	numShards = 256           // Power of 2 for efficient modulo operations
	shardMask = numShards - 1 // Bitmask for fast modulo

	// Pre-computed byte constants for faster comparisons
	spaceChar = byte(' ')
	crChar    = byte('\r')
	lfChar    = byte('\n')
	colonChar = byte(':')
	tabChar   = byte('\t')
)

var (
	blankResponse   = []byte("HTTP/1.1 200 OK\r\nContent-Length: 0\r\n\r\n")
	pathNginxStatus = []byte("/nginx_status")
	pathStatus      = []byte("/status")
	doubleCRLF      = []byte("\r\n\r\n")
	hostHeader      = []byte("host:")

	// Pre-defined subdomain patterns for zero-copy matching
	jsSubdomain      = []byte("js.relayed.network")
	uamSubdomain     = []byte("uam.relayed.network")
	managedSubdomain = []byte("managed.relayed.network")
)

var (
	// Optimized pools with larger initial capacities to reduce reallocations
	statusHeaderPool = sync.Pool{
		New: func() interface{} {
			return make([]byte, 0, 256) // Increased from 128
		},
	}
	statusBodyPool = sync.Pool{
		New: func() interface{} {
			return make([]byte, 0, 512) // Increased from 256
		},
	}

	// Pool for connection info to reduce allocations
	connInfoPool = sync.Pool{
		New: func() interface{} {
			return &ConnectionInfo{}
		},
	}

	// Pool for slice of connections to close (used in OnTick)
	connSlicePool = sync.Pool{
		New: func() interface{} {
			return make([]gnet.Conn, 0, 16) // Pre-allocate for 16 connections
		},
	}
)

// ConnectionInfo holds connection metadata for timeout tracking
type ConnectionInfo struct {
	lastActivity   time.Time
	conn           gnet.Conn
	subdomainIndex int // To track which subdomain this connection belongs to
}

// ConnShard represents a single shard of the connection tracker
type ConnShard struct {
	mu    sync.RWMutex
	conns map[int]*ConnectionInfo
}

// ShardedConnTracker implements a sharded connection tracker for better concurrency
type ShardedConnTracker struct {
	shards [numShards]ConnShard
}

// NewShardedConnTracker creates a new sharded connection tracker
func NewShardedConnTracker() *ShardedConnTracker {
	tracker := &ShardedConnTracker{}
	for i := 0; i < numShards; i++ {
		tracker.shards[i].conns = make(map[int]*ConnectionInfo)
	}
	return tracker
}

// getShard returns the shard for a given file descriptor
func (sct *ShardedConnTracker) getShard(fd int) *ConnShard {
	return &sct.shards[fd&shardMask]
}

// Store stores a connection info in the appropriate shard
func (sct *ShardedConnTracker) Store(fd int, connInfo *ConnectionInfo) {
	shard := sct.getShard(fd)
	shard.mu.Lock()
	shard.conns[fd] = connInfo
	shard.mu.Unlock()
}

// Load loads a connection info from the appropriate shard
func (sct *ShardedConnTracker) Load(fd int) (*ConnectionInfo, bool) {
	shard := sct.getShard(fd)
	shard.mu.RLock()
	connInfo, ok := shard.conns[fd]
	shard.mu.RUnlock()
	return connInfo, ok
}

// LoadAndDelete loads and deletes a connection info from the appropriate shard
func (sct *ShardedConnTracker) LoadAndDelete(fd int) (*ConnectionInfo, bool) {
	shard := sct.getShard(fd)
	shard.mu.Lock()
	connInfo, ok := shard.conns[fd]
	if ok {
		delete(shard.conns, fd)
	}
	shard.mu.Unlock()
	return connInfo, ok
}

// Range iterates over all connections in all shards
func (sct *ShardedConnTracker) Range(fn func(fd int, connInfo *ConnectionInfo) bool) {
	for i := 0; i < numShards; i++ {
		shard := &sct.shards[i]
		shard.mu.RLock()
		for fd, connInfo := range shard.conns {
			if !fn(fd, connInfo) {
				shard.mu.RUnlock()
				return
			}
		}
		shard.mu.RUnlock()
	}
}

// SubdomainStats holds statistics for a specific subdomain
type SubdomainStats struct {
	connections int64
	accepts     int64
	handled     int64
	requests    int64
	reading     int64
	// RPS tracking
	lastRequests int64
	currentRPS   int64
}

// Subdomain indices for array-based lookup (faster than map)
const (
	subdomainJS = iota
	subdomainUAM
	subdomainManaged
	subdomainDefault
	subdomainCount
)

// Subdomain names for display
var subdomainNames = [subdomainCount]string{
	subdomainJS:      "js.relayed.network",
	subdomainUAM:     "uam.relayed.network",
	subdomainManaged: "managed.relayed.network",
	subdomainDefault: "default/other",
}

type rpsServer struct {
	gnet.BuiltinEventEngine
	addr      string
	multicore bool
	eng       gnet.Engine
	// Global stats
	connections int64
	// Pre-allocated per-subdomain stats (no map lookups, no mutex needed)
	subdomainStats [subdomainCount]SubdomainStats
	// Connection tracking for timeouts - now using sharded map for better concurrency
	connTracker *ShardedConnTracker
	// UI state
	startTime    time.Time
	uiTicker     *time.Ticker
	lastUIUpdate time.Time
	uiCancel     context.CancelFunc
	// PGO profiling server
	pprofServer *http.Server
}

// extractSubdomainIndex extracts subdomain index from host header value using optimized byte operations
func extractSubdomainIndex(hostValue []byte) int {
	// Remove port if present (find colon) - optimized with direct byte comparison
	for i, b := range hostValue {
		if b == colonChar {
			hostValue = hostValue[:i]
			break
		}
	}

	// Fast length-based pre-filtering before byte comparison
	hostLen := len(hostValue)
	switch hostLen {
	case len(jsSubdomain):
		if bytes.Equal(hostValue, jsSubdomain) {
			return subdomainJS
		}
	case len(uamSubdomain):
		if bytes.Equal(hostValue, uamSubdomain) {
			return subdomainUAM
		}
	case len(managedSubdomain):
		if bytes.Equal(hostValue, managedSubdomain) {
			return subdomainManaged
		}
	}

	// Default for unknown hosts
	return subdomainDefault
}

func (rs *rpsServer) OnBoot(eng gnet.Engine) (action gnet.Action) {
	rs.eng = eng
	rs.startTime = time.Now()
	rs.lastUIUpdate = time.Now()

	// Initialize sharded connection tracker
	rs.connTracker = NewShardedConnTracker()

	// Start pprof server for PGO profiling on a separate port
	rs.pprofServer = &http.Server{
		Addr: ":6060",
	}
	go func() {
		log.Printf("Starting pprof server on :6060 for PGO profiling")
		if err := rs.pprofServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Printf("pprof server error: %v", err)
		}
	}()

	// Start UI update goroutine with context for graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())
	rs.uiCancel = cancel
	go rs.runUI(ctx)

	// No need to initialize subdomainStats array - zero values are fine
	return
}

func (rs *rpsServer) OnOpen(c gnet.Conn) (out []byte, action gnet.Action) {
	atomic.AddInt64(&rs.connections, 1)

	// Track connection for timeout management using pool to reduce allocations
	now := time.Now()
	connInfo := connInfoPool.Get().(*ConnectionInfo)
	connInfo.lastActivity = now
	connInfo.conn = c
	connInfo.subdomainIndex = -1 // -1 indicates unknown subdomain
	rs.connTracker.Store(c.Fd(), connInfo)

	return
}

func (rs *rpsServer) OnClose(c gnet.Conn, err error) (action gnet.Action) {
	atomic.AddInt64(&rs.connections, -1)

	// Remove connection from tracker and return to pool
	if connInfo, ok := rs.connTracker.LoadAndDelete(c.Fd()); ok {
		// Decrement subdomain connection count if it was assigned
		if connInfo.subdomainIndex != -1 {
			atomic.AddInt64(&rs.subdomainStats[connInfo.subdomainIndex].connections, -1)
		}
		// Clear the connection info before returning to pool
		connInfo.conn = nil
		connInfoPool.Put(connInfo)
	}

	return
}

func (rs *rpsServer) OnTraffic(c gnet.Conn) (action gnet.Action) {
	// Update last activity time for timeout tracking
	var connInfo *ConnectionInfo
	if info, ok := rs.connTracker.Load(c.Fd()); ok {
		connInfo = info
		connInfo.lastActivity = time.Now()
	}

	// Peek(-1) returns all bytes in the ring-buffer without copying. This is a zero-copy operation.
	buf, err := c.Peek(-1)
	if err != nil {
		return gnet.Close
	}

	// Early size check to prevent processing oversized requests
	if len(buf) > maxRequestSize {
		return gnet.Close
	}

	// Find the end of the HTTP request, marked by a double CRLF.
	idx := bytes.Index(buf, doubleCRLF)
	if idx < 0 {
		// Incomplete request, wait for more data.
		return gnet.None
	}
	requestLen := idx + len(doubleCRLF)

	// Defer the discard operation. It will be executed on all return paths from this point forward,
	// ensuring the processed data is removed from the buffer before the connection is closed.
	defer func() {
		_, _ = c.Discard(requestLen)
	}()

	// Create a slice that views the request data from the buffer. This is also a zero-copy operation.
	data := buf[:requestLen]

	// Parse HTTP request to extract path and host - optimized parsing
	lineEnd := bytes.IndexByte(data, lfChar)
	if lineEnd < 0 {
		c.Write(blankResponse)
		return gnet.Close
	}
	requestLine := data[:lineEnd]
	if len(requestLine) > 0 && requestLine[len(requestLine)-1] == crChar {
		requestLine = requestLine[:len(requestLine)-1]
	}

	// Extract path from request line
	firstSpace := bytes.IndexByte(requestLine, spaceChar)
	if firstSpace < 0 {
		c.Write(blankResponse)
		return gnet.Close
	}
	pathAndQuery := requestLine[firstSpace+1:]
	secondSpace := bytes.IndexByte(pathAndQuery, spaceChar)
	var path []byte
	if secondSpace < 0 {
		path = pathAndQuery
	} else {
		path = pathAndQuery[:secondSpace]
	}

	// Extract Host header using optimized zero-copy byte operations
	subdomainIndex := subdomainDefault
	headerStart := lineEnd + 1

	// Limit header parsing to prevent DoS attacks
	maxHeaderEnd := headerStart + maxHeaderSize
	if maxHeaderEnd > len(data) {
		maxHeaderEnd = len(data)
	}

	for headerStart < maxHeaderEnd {
		headerEnd := bytes.IndexByte(data[headerStart:maxHeaderEnd], lfChar)
		if headerEnd < 0 {
			break
		}
		headerEnd += headerStart

		headerLine := data[headerStart:headerEnd]
		if len(headerLine) > 0 && headerLine[len(headerLine)-1] == crChar {
			headerLine = headerLine[:len(headerLine)-1]
		}

		// Optimized case-insensitive comparison for "host:" header
		if len(headerLine) >= 5 && bytes.EqualFold(headerLine[:5], hostHeader) {

			// Extract host value (skip "host:" and trim spaces) - optimized trimming
			hostStart := 5
			for hostStart < len(headerLine) && (headerLine[hostStart] == spaceChar || headerLine[hostStart] == tabChar) {
				hostStart++
			}
			if hostStart < len(headerLine) {
				hostValue := headerLine[hostStart:]
				subdomainIndex = extractSubdomainIndex(hostValue)
			}
			break
		}

		headerStart = headerEnd + 1
	}

	// Update per-subdomain statistics using direct array access (no locks, no allocations)
	subStats := &rs.subdomainStats[subdomainIndex]
	atomic.AddInt64(&subStats.requests, 1)
	atomic.AddInt64(&subStats.reading, 1)
	defer atomic.AddInt64(&subStats.reading, -1)

	// Associate connection with subdomain if not already done
	if connInfo != nil && connInfo.subdomainIndex == -1 {
		connInfo.subdomainIndex = subdomainIndex
		atomic.AddInt64(&subStats.connections, 1)
	}

	// For nginx status compatibility, let's assume accepts/handled are per-request
	atomic.AddInt64(&subStats.accepts, 1)
	atomic.AddInt64(&subStats.handled, 1)

	if bytes.Equal(path, pathNginxStatus) || bytes.Equal(path, pathStatus) {
		// Use subdomain-specific statistics
		activeConnections := atomic.LoadInt64(&rs.subdomainStats[subdomainIndex].connections)
		reading := atomic.LoadInt64(&subStats.reading)
		waiting := activeConnections - reading
		if waiting < 0 {
			waiting = 0
		}

		subdomainAccepts := atomic.LoadInt64(&subStats.accepts)
		subdomainHandled := atomic.LoadInt64(&subStats.handled)
		subdomainRequests := atomic.LoadInt64(&subStats.requests)

		// build body in exact nginx status format
		body := statusBodyPool.Get().([]byte)
		body = body[:0]
		body = append(body, "Active connections: "...)
		body = strconv.AppendInt(body, activeConnections, 10)
		body = append(body, " \nserver accepts handled requests\n "...)
		body = strconv.AppendInt(body, subdomainAccepts, 10)
		body = append(body, ' ')
		body = strconv.AppendInt(body, subdomainHandled, 10)
		body = append(body, ' ')
		body = strconv.AppendInt(body, subdomainRequests, 10)
		body = append(body, " \nReading: "...)
		body = strconv.AppendInt(body, reading, 10)
		body = append(body, " Writing: "...)
		body = strconv.AppendInt(body, 0, 10) // gnet doesn't expose writing stats directly
		body = append(body, " Waiting: "...)
		body = strconv.AppendInt(body, waiting, 10)
		body = append(body, " \n"...)

		// build header
		header := statusHeaderPool.Get().([]byte)
		header = header[:0]
		header = append(header, "HTTP/1.1 200 OK\r\nContent-Type: text/plain\r\nContent-Length: "...)
		header = strconv.AppendInt(header, int64(len(body)), 10)
		header = append(header, "\r\n\r\n"...)

		c.Writev([][]byte{header, body})
		statusHeaderPool.Put(header)
		statusBodyPool.Put(body)
	} else {
		c.Write(blankResponse)
	}

	return gnet.Close
}

// runUI runs the text-based UI in a separate goroutine
func (rs *rpsServer) runUI(ctx context.Context) {
	ticker := time.NewTicker(uiUpdateInterval)
	defer ticker.Stop()

	// Clear screen and hide cursor
	fmt.Print("\033[2J\033[?25l")

	for {
		select {
		case <-ticker.C:
			rs.updateUI()
		case <-ctx.Done():
			return
		}
	}
}

// updateUI refreshes the console display using optimized buffered output
func (rs *rpsServer) updateUI() {
	now := time.Now()
	uptime := now.Sub(rs.startTime)

	// Calculate RPS for each subdomain
	for i := 0; i < subdomainCount; i++ {
		currentRequests := atomic.LoadInt64(&rs.subdomainStats[i].requests)
		lastRequests := atomic.LoadInt64(&rs.subdomainStats[i].lastRequests)
		rps := currentRequests - lastRequests
		atomic.StoreInt64(&rs.subdomainStats[i].currentRPS, rps)
		atomic.StoreInt64(&rs.subdomainStats[i].lastRequests, currentRequests)
	}

	// Use strings.Builder for efficient buffered output (optimization from plan)
	var builder strings.Builder
	builder.Grow(2048) // Pre-allocate buffer to reduce reallocations

	// Move cursor to top-left and clear screen
	builder.WriteString("\033[H\033[2J")

	// Header
	builder.WriteString("╔══════════════════════════════════════════════════════════════════════════════╗\n")
	builder.WriteString("║                            RPS Server Monitor                               ║\n")
	builder.WriteString("╠══════════════════════════════════════════════════════════════════════════════╣\n")
	fmt.Fprintf(&builder, "║ Uptime: %-20s │ Total Active Connections: %-10d │ Port: 9000    ║\n",
		rs.formatDuration(uptime), atomic.LoadInt64(&rs.connections))
	builder.WriteString("╠══════════════════════════════════════════════════════════════════════════════╣\n")
	builder.WriteString("║                              Subdomain Statistics                           ║\n")
	builder.WriteString("╠═══════════════════════════════╤═══════╤═══════════╤═══════╤═══════╤═══════════╣\n")
	builder.WriteString("║ Subdomain                     │  RPS  │ Requests  │ Active│ Read  │ Handled   ║\n")
	builder.WriteString("╠═══════════════════════════════╪═══════╪═══════════╪═══════╪═══════╪═══════════╣\n")

	// Subdomain stats
	for i := 0; i < subdomainCount; i++ {
		stats := &rs.subdomainStats[i]
		name := subdomainNames[i]
		if len(name) > 29 {
			name = name[:26] + "..."
		}

		rps := atomic.LoadInt64(&stats.currentRPS)
		total := atomic.LoadInt64(&stats.requests)
		connections := atomic.LoadInt64(&stats.connections)
		reading := atomic.LoadInt64(&stats.reading)
		handled := atomic.LoadInt64(&stats.handled)

		fmt.Fprintf(&builder, "║ %-29s │ %5d │ %9d │ %5d │ %5d │ %9d ║\n",
			name, rps, total, connections, reading, handled)
	}

	builder.WriteString("╚═══════════════════════════════╧═══════╧═══════════╧═══════╧═══════╧═══════════╝\n")
	builder.WriteString("\nPress Ctrl+C to stop the server\n")
	fmt.Fprintf(&builder, "Last updated: %s\n", now.Format("15:04:05"))

	// Single write operation to console (optimization from plan)
	fmt.Print(builder.String())
}

// formatDuration formats a duration in a human-readable way
func (rs *rpsServer) formatDuration(d time.Duration) string {
	if d < time.Minute {
		return fmt.Sprintf("%.0fs", d.Seconds())
	} else if d < time.Hour {
		return fmt.Sprintf("%.0fm %.0fs", d.Minutes(), math.Mod(d.Seconds(), 60))
	} else {
		hours := int(d.Hours())
		minutes := int(d.Minutes()) - hours*60
		return fmt.Sprintf("%dh %dm", hours, minutes)
	}
}

// OnTick implements periodic timeout checking for connections - optimized with pool and sharding
func (rs *rpsServer) OnTick() (delay time.Duration, action gnet.Action) {
	now := time.Now()

	// Get connection slice from pool to reduce allocations
	connectionsToClose := connSlicePool.Get().([]gnet.Conn)
	connectionsToClose = connectionsToClose[:0] // Reset length but keep capacity

	// Iterate through all tracked connections to find timed-out ones using sharded tracker
	rs.connTracker.Range(func(fd int, connInfo *ConnectionInfo) bool {
		if now.Sub(connInfo.lastActivity) > connectionTimeout {
			// Collect connections to close outside the Range loop
			// to avoid modifying the map while iterating
			connectionsToClose = append(connectionsToClose, connInfo.conn)
		}
		return true // continue iteration
	})

	// Close timed-out connections
	if len(connectionsToClose) > 0 {
		log.Printf("Closing %d timed-out connections (idle > %v)", len(connectionsToClose), connectionTimeout)
		for _, conn := range connectionsToClose {
			conn.Close() // This will trigger OnClose which removes from tracker
		}
	}

	// Return slice to pool
	connSlicePool.Put(connectionsToClose)

	// Return cleanup interval for next tick
	return cleanupInterval, gnet.None
}

// Shutdown gracefully stops the UI goroutine and pprof server
func (rs *rpsServer) Shutdown() {
	if rs.uiCancel != nil {
		rs.uiCancel()
	}
	if rs.pprofServer != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		rs.pprofServer.Shutdown(ctx)
	}
}

func main() {
	rs := &rpsServer{
		addr:      "tcp://:9000",
		multicore: true,
	}

	// Setup graceful shutdown to restore cursor and stop UI goroutine
	defer func() {
		rs.Shutdown()          // Stop UI goroutine gracefully
		fmt.Print("\033[?25h") // Show cursor
		if r := recover(); r != nil {
			log.Printf("Server panic: %v", r)
		}
	}()

	log.Printf("Starting RPS server on %s", rs.addr)
	log.Printf("Connection timeout: %v", connectionTimeout)
	log.Printf("Cleanup interval: %v", cleanupInterval)
	log.Printf("Multicore mode: %v", rs.multicore)
	log.Printf("Connection tracker shards: %d", numShards)
	log.Printf("pprof server available on :6060 for PGO profiling")
	log.Printf("UI will start after server boot...")

	// Performance optimizations for gnet
	options := []gnet.Option{
		gnet.WithMulticore(rs.multicore),
		gnet.WithReusePort(true),             // Enable SO_REUSEPORT for better load balancing
		gnet.WithTCPKeepAlive(time.Minute),   // Enable TCP keepalive
		gnet.WithTCPNoDelay(gnet.TCPNoDelay), // Disable Nagle's algorithm for lower latency
		gnet.WithSocketRecvBuffer(64 * 1024), // Increase receive buffer
		gnet.WithSocketSendBuffer(64 * 1024), // Increase send buffer
	}

	log.Fatal(gnet.Run(rs, rs.addr, options...))
}
