# Profile-Guided Optimization (PGO) Setup

This document explains how to use Profile-Guided Optimization (PGO) to further optimize the RPS server performance.

## What is PGO?

Profile-Guided Optimization (PGO) allows the Go compiler to make more intelligent optimization decisions based on real-world usage patterns. The compiler uses profiling data to optimize the "hot path" - the code that runs most frequently under actual load.

## Steps to Enable PGO

### 1. Build and Run the Server

First, build and run the server normally with optimized flags:

```bash
# On Windows (PowerShell)
$env:GOAMD64="v4"; go build -ldflags "-s -w" -o web main.go
./web

# On Linux/macOS
GOAMD64=v4 go build -ldflags "-s -w" -o web main.go
./web
```

The server will automatically start a pprof server on port 6060 for profiling.

### 2. Generate Load

Apply a realistic high-RPS load to the server using a tool like `wrk`, `bombardier`, or `hey`. For example:

```bash
# Using wrk (install from https://github.com/wg/wrk)
wrk -t12 -c400 -d30s --latency http://localhost:9000/nginx_status

# Using bombardier (install: go install github.com/codesenberg/bombardier@latest)
bombardier -c 200 -d 30s http://localhost:9000/nginx_status

# Using hey (install: go install github.com/rakyll/hey@latest)
hey -z 30s -c 200 http://localhost:9000/nginx_status
```

### 3. Collect CPU Profile

While the load test is running, collect a CPU profile:

```bash
# Collect 30-second CPU profile
go tool pprof http://localhost:6060/debug/pprof/profile?seconds=30
```

This will download the profile and save it locally. Note the filename (e.g., `pprof.main.samples.cpu.001.pb.gz`).

### 4. Save Profile as default.pgo

Copy the profile file to your project directory and rename it to `default.pgo`:

```bash
cp pprof.main.samples.cpu.001.pb.gz default.pgo
```

### 5. Build with PGO

Now build the server with PGO enabled and optimized flags:

```bash
# On Windows (PowerShell)
$env:GOAMD64="v4"; go build -pgo=auto -ldflags "-s -w" -o web-pgo main.go

# On Linux/macOS
GOAMD64=v4 go build -pgo=auto -ldflags "-s -w" -o web-pgo main.go
```

The `-pgo=auto` flag tells the Go compiler to automatically find and use the `default.pgo` file.

### 6. Compare Performance

Run performance tests on both versions to compare:

```bash
# Test original version
./web &
# Run your load test and measure performance
kill %1

# Test PGO-optimized version
./web-pgo &
# Run the same load test and compare results
kill %1
```

## Expected Improvements

PGO typically provides:
- 2-14% improvement in CPU performance
- Better instruction cache utilization
- More aggressive inlining of hot functions
- Improved register allocation

## Tips

1. **Use realistic load patterns**: The profile should represent your actual production workload
2. **Profile duration**: 30-60 seconds is usually sufficient
3. **Multiple profiles**: You can merge multiple profiles for better coverage
4. **Regular updates**: Re-profile periodically as your application evolves

## Troubleshooting

- **No default.pgo found**: Ensure the file is in the same directory as your Go source
- **Profile too small**: Increase the profiling duration or load intensity
- **No improvement**: PGO works best with CPU-bound applications; network-bound applications may see smaller gains

## Advanced Usage

For more advanced PGO usage, see the official Go documentation:
https://go.dev/doc/pgo
